{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 6269644749152266277, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[6416823254013318197, "tauri_plugin_fs", false, 3884420465574945679], [7083681497971801814, "pick_up_memories_lib", false, 18083370453853741431], [7083681497971801814, "build_script_build", false, 15538155896760839533], [7760050409050412348, "tauri_plugin_notification", false, 538692629770370360], [9689903380558560274, "serde", false, 15388555481042839483], [9897246384292347999, "chrono", false, 8226066252664292969], [14039947826026167952, "tauri", false, 12664068235939240374], [14525517306681678134, "tauri_plugin_dialog", false, 9047606968378946554], [15367738274754116744, "serde_json", false, 6710690727211752251], [16702348383442838006, "tauri_plugin_opener", false, 17485070294440639636]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pick-up-memories-6394fb107d2d1f3c\\dep-bin-pick-up-memories", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}