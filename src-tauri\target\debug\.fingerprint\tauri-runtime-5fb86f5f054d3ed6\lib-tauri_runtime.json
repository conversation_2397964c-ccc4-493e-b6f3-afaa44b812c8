{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 15042535163053410885, "deps": [[2671782512663819132, "tauri_utils", false, 12528380575152632993], [3150220818285335163, "url", false, 1416861958194483817], [4143744114649553716, "raw_window_handle", false, 17105436284664801499], [6089812615193535349, "build_script_build", false, 9648374566902302023], [7606335748176206944, "dpi", false, 17470456338001651179], [9010263965687315507, "http", false, 11446004077522401511], [9689903380558560274, "serde", false, 15388555481042839483], [10806645703491011684, "thiserror", false, 14050447952190329466], [14585479307175734061, "windows", false, 16004233624952482235], [15367738274754116744, "serde_json", false, 6710690727211752251], [16727543399706004146, "cookie", false, 1780594545582981424]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-5fb86f5f054d3ed6\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}