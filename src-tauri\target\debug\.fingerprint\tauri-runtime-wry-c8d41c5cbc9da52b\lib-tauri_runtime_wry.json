{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 11292800727680179980, "deps": [[376837177317575824, "softbuffer", false, 16869998451440061924], [2013030631243296465, "webview2_com", false, 3847180084315592198], [2671782512663819132, "tauri_utils", false, 12528380575152632993], [3150220818285335163, "url", false, 1416861958194483817], [3722963349756955755, "once_cell", false, 17203810275947670552], [4143744114649553716, "raw_window_handle", false, 17105436284664801499], [5986029879202738730, "log", false, 14769126643489177774], [6089812615193535349, "tauri_runtime", false, 7456916033740050946], [8826339825490770380, "tao", false, 14852054453716144072], [9010263965687315507, "http", false, 11446004077522401511], [9141053277961803901, "wry", false, 10920852749541046964], [11599800339996261026, "build_script_build", false, 12064247415895716416], [14585479307175734061, "windows", false, 16004233624952482235]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-c8d41c5cbc9da52b\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}