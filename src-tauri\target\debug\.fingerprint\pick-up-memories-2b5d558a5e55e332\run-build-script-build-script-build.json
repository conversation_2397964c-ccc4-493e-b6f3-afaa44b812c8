{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[7083681497971801814, "build_script_build", false, 13746350061523916445], [14039947826026167952, "build_script_build", false, 15451729950648944262], [14525517306681678134, "build_script_build", false, 11611073615909173327], [6416823254013318197, "build_script_build", false, 14377748374265472782], [7760050409050412348, "build_script_build", false, 16453549644116911183], [16702348383442838006, "build_script_build", false, 2149803730810870349]], "local": [{"RerunIfChanged": {"output": "debug\\build\\pick-up-memories-2b5d558a5e55e332\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}