{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 15657897354478470176, "path": 5081336466920372136, "deps": [[1462335029370885857, "quick_xml", false, 7698269834997009413], [3334271191048661305, "windows_version", false, 7639004582749026685], [10806645703491011684, "thiserror", false, 14050447952190329466], [14585479307175734061, "windows", false, 16004233624952482235]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-a756a9f7fe216ccb\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}