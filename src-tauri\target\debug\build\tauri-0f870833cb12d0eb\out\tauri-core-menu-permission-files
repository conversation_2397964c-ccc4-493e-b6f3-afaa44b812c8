["\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\Flutter or Tauri or Ionic or Hono applications\\Pick-Up-Memories\\src-tauri\\target\\debug\\build\\tauri-0f870833cb12d0eb\\out\\permissions\\menu\\autogenerated\\default.toml"]