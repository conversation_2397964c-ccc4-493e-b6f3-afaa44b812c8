{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 16103601952071245633, "profile": 8731458305071235362, "path": 10763286916239946207, "deps": [[6416823254013318197, "tauri_plugin_fs", false, 3884420465574945679], [7083681497971801814, "build_script_build", false, 15538155896760839533], [7760050409050412348, "tauri_plugin_notification", false, 538692629770370360], [9689903380558560274, "serde", false, 15388555481042839483], [9897246384292347999, "chrono", false, 8226066252664292969], [14039947826026167952, "tauri", false, 12664068235939240374], [14525517306681678134, "tauri_plugin_dialog", false, 9047606968378946554], [15367738274754116744, "serde_json", false, 6710690727211752251], [16702348383442838006, "tauri_plugin_opener", false, 17485070294440639636]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pick-up-memories-6394fb107d2d1f3c\\dep-lib-pick_up_memories_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}